<?php

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return 'It works ' . now()->format('c');
});

// Login route for API authentication redirects
Route::get('/login', function () {
    return response()->json(['message' => 'Unauthenticated'], 401);
})->name('login');

// Authentication Routes
Route::prefix('access')->group(function () {
    // Registration and login pages
    Route::get('/register', [App\Http\Controllers\AuthController::class, 'showRegister'])
        ->name('auth.register');

    Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLogin'])
        ->name('auth.login');
});

// VPN Subscription Routes
Route::prefix('access')->group(function () {

    Route::any('/{uuid}', [App\Http\Controllers\Access\ProfileController::class, 'show'])
        ->whereUuid('uuid')
        ->middleware('log.subscription.access')
        ->name('access.profile');

    // Update routing preference
    Route::post('/{uuid}/settings/routing-preference', [App\Http\Controllers\Access\ProfileController::class, 'updateRoutingPreference'])
        ->whereUuid('uuid')
        ->name('access.routing.preference');

    // Update SNI category
    Route::post('/{uuid}/settings/sni-category', [App\Http\Controllers\Access\ProfileController::class, 'updateSniCategory'])
        ->whereUuid('uuid')
        ->name('access.sni.category');

    // Display available subscription plans + payment methods
    Route::get('/{uuid}/plan/select', [App\Http\Controllers\Access\PlanSelectionController::class, 'show'])
        ->whereUuid('uuid')
        ->name('access.plan.selection');

    // Process plan selection and payment method → generate payment
    Route::post('/{uuid}/plan/purchase', [App\Http\Controllers\Access\VpnPurchaseController::class, 'purchase'])
        ->whereUuid('uuid')
        ->name('access.plan.purchase');

    // Renewal routes
    Route::get('/{uuid}/renew', [App\Http\Controllers\Access\RenewalController::class, 'show'])
        ->whereUuid('uuid')
        ->name('access.renewal.show');

    Route::post('/{uuid}/renew', [App\Http\Controllers\Access\RenewalController::class, 'renew'])
        ->whereUuid('uuid')
        ->name('access.renewal.renew');
});

Route::prefix('support')->group(function () {
    Route::any('/{uuid}', [App\Http\Controllers\Support\SupportController::class, 'show'])
        ->whereUuid('uuid')
        ->name('support.index');

    Route::post('/{uuid}/rating', [App\Http\Controllers\Support\SupportController::class, 'submitRating'])
        ->whereUuid('uuid')
        ->name('support.rating');
});

// Payment result routes
Route::prefix('orders')->group(function () {
    Route::get('/{orderPublicId}/success', function (string $orderPublicId) {
        $order = Order::findByPublicId($orderPublicId)->firstOrFail();
        return view('access.payment.success', compact('order'));
    })->name('access.payment.success');

    Route::get('/{orderPublicId}/failed', function (string $orderPublicId) {
        $order = Order::findByPublicId($orderPublicId)->firstOrFail();
        return view('access.payment.failed', compact('order'));
    })->name('access.payment.failed');
});

// Payment Webhook Routes (no CSRF protection)
Route::prefix('webhook')->group(function () {
    Route::post('/tbank', [App\Http\Controllers\WebhookController::class, 'tbank'])
        ->name('webhook.tbank');

    Route::post('/{gateway}', [App\Http\Controllers\WebhookController::class, 'handle'])
        ->name('webhook.handle');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Telescope Auth Routes (БЕЗ middleware)
Route::get('/telescope/login', [App\Http\Controllers\TelescopeController::class, 'showLogin']);
Route::post('/telescope/login', [App\Http\Controllers\TelescopeController::class, 'login']);
Route::get('/telescope/logout', [App\Http\Controllers\TelescopeController::class, 'logout']);

// Admin Panel Routes
Route::prefix('admin')->group(function () {
    Route::get('/{any?}', function () {
        return view('admin');
    })->where('any', '.*')->name('admin');
});

