<!-- Telescope Logout Button -->
<style>
.telescope-logout {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    align-items: center;
    gap: 8px;
}

.telescope-logout-user {
    font-weight: 500;
}

.telescope-logout-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s;
}

.telescope-logout-btn:hover {
    background: #dc2626;
}
</style>

<div class="telescope-logout">
    <span class="telescope-logout-user">{{ session('telescope_user', 'Admin') }}</span>
    <a href="/telescope/logout" class="telescope-logout-btn">Выйти</a>
</div>

<script>
// Добавляем кнопку выхода только если мы в Telescope
if (window.location.pathname.startsWith('/telescope') && !window.location.pathname.includes('/login')) {
    document.addEventListener('DOMContentLoaded', function() {
        // Кнопка уже добавлена через Blade template
        console.log('Telescope logout button loaded');
    });
}
</script>
