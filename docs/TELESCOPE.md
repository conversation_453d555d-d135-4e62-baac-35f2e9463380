# Laravel Telescope - Интеграция

## Обзор

Laravel Telescope успешно интегрирован в проект с простой и надежной авторизацией через таблицу `admin_users`.

## Доступ к Telescope

### URL
- **Основной интерфейс**: `https://your-domain.com/telescope`
- **Страница входа**: `https://your-domain.com/telescope/login`
- **Выход**: `https://your-domain.com/telescope/logout`

### Авторизация

#### В локальной среде (APP_ENV=local)
- Доступ открыт для всех без авторизации

#### В продакшн среде
- Требуется авторизация через активных админов из таблицы `admin_users`
- При попытке доступа к `/telescope` без авторизации автоматически перенаправляет на `/telescope/login`

### Учетные записи
- **Любой активный админ** из таблицы `admin_users`
- Требования: `is_active = true`
- Используется `username` и `password` из базы данных

## Архитектура

### Компоненты системы:

1. **TelescopeController** (`app/Http/Controllers/TelescopeController.php`)
   - Обработка авторизации
   - Методы: `showLogin()`, `login()`, `logout()`

2. **TelescopeMiddleware** (`app/Http/Middleware/TelescopeMiddleware.php`)
   - Защита маршрутов Telescope
   - Перенаправление неавторизованных пользователей

3. **TelescopeServiceProvider** (`app/Providers/TelescopeServiceProvider.php`)
   - Настройка Gate для авторизации
   - Фильтрация записей

4. **Страница входа** (`resources/views/telescope-login.blade.php`)
   - Простая форма авторизации
   - Современный дизайн

### Маршруты:
```php
GET  /telescope/login  - Страница входа
POST /telescope/login  - Обработка входа
GET  /telescope/logout - Выход
GET  /telescope        - Основной интерфейс (защищен middleware)
```

## Настройка

### Environment переменные

```env
# Основные настройки
TELESCOPE_ENABLED=true

# Для локальной разработки (без авторизации)
APP_ENV=local

# Для продакшн (с авторизацией)
APP_ENV=production
```

### Отключение отдельных watchers

```env
# Отключить мониторинг запросов к БД
TELESCOPE_QUERY_WATCHER=false

# Отключить мониторинг моделей
TELESCOPE_MODEL_WATCHER=false

# Отключить мониторинг представлений
TELESCOPE_VIEW_WATCHER=false
```

## Безопасность

### Изоляция от админской системы:
- ✅ Отдельная сессия (`telescope_auth`)
- ✅ Не конфликтует с `admin.auth` middleware
- ✅ Не влияет на работу админской панели
- ✅ Независимая авторизация

### Рекомендации для продакшн:
1. Регулярно очищайте старые записи:
   ```bash
   php artisan telescope:prune --hours=48
   ```

2. Добавьте в cron:
   ```bash
   0 2 * * * php artisan telescope:prune --hours=48
   ```

3. Отключите ненужные watchers для экономии ресурсов

## Использование

### Вход в систему:
1. Откройте `https://your-domain.com/telescope`
2. Если не авторизованы, перенаправит на `/telescope/login`
3. Введите username и password любого активного админа
4. После входа откроется интерфейс Telescope

### Выход:
- Перейдите на `https://your-domain.com/telescope/logout`
- Или используйте кнопку выхода в интерфейсе (если добавлена)

## Мониторинг

### Доступные разделы:
- **Requests** - HTTP запросы
- **Commands** - Artisan команды  
- **Schedule** - Запланированные задачи
- **Jobs** - Фоновые задачи
- **Exceptions** - Исключения и ошибки
- **Logs** - Логи приложения
- **Queries** - SQL запросы
- **Models** - Операции с моделями
- **Events** - События приложения
- **Mail** - Отправленные письма
- **Cache** - Операции с кэшем
- **Redis** - Операции с Redis

## Troubleshooting

### Telescope не отображает данные
1. Проверьте `TELESCOPE_ENABLED=true`
2. Убедитесь, что нужные watchers включены
3. Проверьте права доступа к БД

### Ошибки авторизации
1. Проверьте правильность логина/пароля
2. Убедитесь, что админ активен (`is_active = true`)
3. Проверьте настройки в `TelescopeServiceProvider`

### Высокое потребление ресурсов
1. Отключите ненужные watchers
2. Настройте регулярную очистку данных
3. Ограничьте размер записываемых данных

## Интеграция завершена

✅ Laravel Telescope установлен и настроен  
✅ Простая авторизация через admin_users  
✅ Полная изоляция от админской системы  
✅ Автоматическое перенаправление  
✅ Стандартная конфигурация  
✅ Билд фронтенда проходит успешно  
✅ Все маршруты работают корректно  

**Telescope доступен по адресу `/telescope` с надежной авторизацией!** 🚀
