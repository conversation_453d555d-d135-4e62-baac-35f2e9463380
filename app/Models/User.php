<?php

namespace App\Models;

use App\Services\MoneyService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasUuids, SoftDeletes, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'real_email',
        'real_email_verified_at',
        'tg_id',
        'old_client_id',
        'leadteh_contact_id',
        'email_verified_at',
        'password',
        'is_active',
        'registered_at',
        'notes',
        'admin_notes',
        'details',
        'source',
        'use_common_routing',
        'subscription_plan_id',
        'last_online_at',
        'sni_category',
        'currency',
    ];

    protected $appends = [
        'client_id',
        'balance',
        'balance_formatted',
        'access_url',
        'public_client_id',
        'referral_code',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'real_email_verified_at' => 'datetime',
            'registered_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'use_common_routing' => 'boolean',
            'details' => 'array',
            'last_online_at' => 'datetime',
            'sni_category' => 'string',
            'tg_id' => 'integer',
            'leadteh_contact_id' => 'integer',
            'old_client_id' => 'string',
        ];
    }


    /* ---------------- Relationships ---------------- */



    /**
     * Get the referral codes for this user.
     */
    public function referralCodes(): HasMany
    {
        return $this->hasMany(ReferralCode::class);
    }

    /**
     * Get the default and active referral code for this user.
     */
    public function defaultReferralCode(): HasOne
    {
        return $this->hasOne(ReferralCode::class)
                    ->where('is_default', true)
                    ->where('is_active', true);
    }

    /**
     * Get the subscription plan for this user.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the subscription plan for this user. (alias for subscriptionPlan())
     */
    public function plan(): BelongsTo
    {
        return $this->subscriptionPlan();
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's current active subscription.
     */
    public function currentSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
            })
            ->latest();
    }

    /**
     * Get the user's any subscription.
     */
    public function anySubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', '!=', 'canceled')->latest();
    }

    /**
     * Get the user's orders.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the user's balance transactions.
     */
    public function balanceTransactions(): HasMany
    {
        return $this->hasMany(\App\Models\BalanceTransaction::class);
    }

    /**
     * Get the user's traffic logs.
     */
    public function trafficLogs(): HasMany
    {
        return $this->hasMany(UserTrafficLog::class);
    }

    /**
     * Get the user's online logs.
     */
    public function onlineLogs(): HasMany
    {
        return $this->hasMany(UserOnlineLog::class);
    }

    /**
     * Get the user's ratings.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(UserRating::class);
    }

    /**
     * Get the server pools assigned to this user.
     */
    public function serverPools(): BelongsToMany
    {
        return $this->belongsToMany(ServerPool::class, 'user_server_assignments', 'user_id', 'pool_id')
            ->withPivot(['assigned_at', 'released_at'])
            ->withTimestamps();
    }

    /**
     * Get the active server pools assigned to this user.
     */
    public function activeServerPools(): BelongsToMany
    {
        return $this->serverPools()
                ->where('server_pools.is_active', true)
                ->wherePivotNull('released_at');
    }

    /**
     * Get all active servers for this user.
     */
    public function getAllPoolsServers(): \Illuminate\Support\Collection
    {
        return $this->serverPools
            ->load('servers') // важно: eager load
            ->flatMap(fn ($pool) => $pool->servers)
            ->unique('id')
            ->values();
    }


    /**
     * Get the referral records where this user is the inviter.
     */
    public function invitedReferrals(): HasMany
    {
        return $this->hasMany(Referral::class, 'inviter_user_id');
    }

    /**
     * Get the user's fingerprints.
     */
    public function fingerprints(): HasMany
    {
        return $this->hasMany(UserFingerprint::class);
    }

    /**
     * Get the user's authentication logs.
     */
    public function authLogs(): HasMany
    {
        return $this->hasMany(UserAuthLog::class);
    }

    /**
     * Get the user's email verification codes.
     */
    public function emailVerificationCodes(): HasMany
    {
        return $this->hasMany(EmailVerificationCode::class);
    }

    /**
     * Get the referral record where this user was invited.
     */
    public function invitationReferral(): HasOne
    {
        return $this->hasOne(Referral::class, 'invited_user_id');
    }

    /**
     * Get the referral record where this user was invited (alias for invitationReferral).
     */
    public function referral(): HasOne
    {
        return $this->invitationReferral();
    }

    /**
     * Get the subscription history for this user.
     */
    public function subscriptionHistory(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class);
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only users with active subscriptions.
     */
    public function scopeWithActiveSubscription($query)
    {
        return $query->whereHas('currentSubscription');
    }

    /**
     * Scope to get only users without active subscriptions.
     */
    public function scopeWithoutActiveSubscription($query)
    {
        return $query->doesntHave('currentSubscription');
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Generate client email for XUI (user_email + "_" + inbound_id). Antagen to User::extractEmailFromClientEmail()
     */
    public function generateClientEmailForInbound(int $inboundId): string
    {
        return $this->email . '_' . $inboundId;
    }

    /**
     * Get the client ID for this user.
     */
    public function getClientId()
    {
        return $this->old_client_id ?? $this->id;
    }

    /**
     * Get the public client ID for this user.
     */
    public function getPublicClientId()
    {
        // real_email or old_client_id or tg_id or id
        return $this->real_email ?? $this->old_client_id ?? $this->tg_id ?? $this->id;
    }

    /**
     * Get the appended field public_client_id for this user.
     */
    public function getPublicClientIdAttribute()
    {
        return $this->getPublicClientId();
    }

    /**
     * Get the appended field client_id for this user.
     */
    protected function getClientIdAttribute()
    {
        return $this->getClientId();
    }

    public function getBalanceAttribute()
    {
        return $this->getTotalBalanceAmount();
    }

    public function getBalanceFormattedAttribute()
    {
        return MoneyService::formatWithCurrencySymbol($this->getTotalBalanceAmount(), $this->currency);
    }

    public function getReferralCodeAttribute()
    {
        return $this->defaultReferralCode()->first()?->code;
    }

    /* ---------------- State Checks ---------------- */

    /**
     * Check if the user is enabled.
     */
    public function isEnabled()
    {
        return $this->is_active && $this->currentSubscription()->exists();
    }

    /**
     * Check if the user is a messenger.
     */
    public function isSniMessenger(): bool
    {
        return $this->sni_category === 'messengers';
    }

    /**
     * Get the Leadteh contact ID for this user.
     */
    public function getLeadtehContactId(): ?int
    {
        return $this->leadteh_contact_id;
    }

    /**
     * Check if the user has a Leadteh contact ID.
     */
    public function hasLeadtehContactId(): bool
    {
        return $this->getLeadtehContactId() !== null;
    }

    public function isOnline(): bool
    {
        return UserOnlineLog::isUserCurrentlyOnline($this->id);
    }


    /* ---------------- Model Events / Hooks ---------------- */

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Generate unique email if not provided
        static::creating(function (User $user) {
            if (empty($user->email)) {
                $user->email = self::generateUniqueEmail();
            }
        });
    }


    /* ---------------- Actions ---------------- */

    /**
     * Activate the user.
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the user.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Upgrade the user to a new subscription plan.
     */
    public function upgradeSubscription(SubscriptionPlan $newPlan): void
    {
        // update the user's subscription plan
        $this->update(['subscription_plan_id' => $newPlan->id]);
    }

    /**
     * Change the user's subscription plan if it's different from the new plan.
     */
    public function changePlanIfDifferent(SubscriptionPlan $newPlan): void
    {
        if (!$this->subscriptionPlan || $this->subscriptionPlan->id !== $newPlan->id) {
            $this->upgradeSubscription($newPlan);
        }
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Generate a referral code for the user.
     */
    public function generateReferralCode(): string
    {
        return bin2hex(random_bytes(4));
    }

    /**
     * Extract email from client email. Antagen to User::generateClientEmailForInbound()
     */
    public static function extractEmailFromClientEmail(string $clientEmail, ?int $inboundId = null): string
    {
        if (preg_match('/^(.+)_\d+$/', $clientEmail, $matches)) {
            return $matches[1];
        }

        return Str::chopEnd($clientEmail, '_' . $inboundId);
    }


    /* ---------------- Aggregates / Stats ---------------- */


    /* ---------------- Balance Methods ---------------- */

    /**
     * Get total user balance amount in minor currency units (e.g., kopecks, cents).
     * This method calculates balance from balance transactions.
     */
    public function getTotalBalanceAmount(): int
    {
        $credits = $this->balanceTransactions()
            ->where('type', \App\Enums\BalanceTransactionType::CREDIT)
            ->sum('amount');

        $debits = $this->balanceTransactions()
            ->where('type', \App\Enums\BalanceTransactionType::DEBIT)
            ->sum('amount');

        return $credits - $debits;
    }

    /**
     * Get user balance in minor currency units (e.g., rubles, dollars).
     */
    public function getBalance(): int
    {
        return $this->getTotalBalanceAmount();
    }

    /**
     * Get user balance in minor currency units (e.g., kopecks, cents).
     */
    public function getBalanceInMinorUnits(): int
    {
        return $this->getTotalBalanceAmount();
    }

    /**
     * Check if user can afford a specific amount.
     *
     * @param int $amount Amount in minor currency units
     * @return bool
     */
    public function canAfford(int $amount): bool
    {
        if ($amount <= 0) {
            return true;
        }

        $balanceAfterDeduction = $this->getTotalBalanceAmount() - $amount;


        return $balanceAfterDeduction >= 0;

        // TODO: Allow some overdraft
    }

    /**
     * Check if user has positive balance.
     */
    public function hasPositiveBalance(): bool
    {
        return $this->getTotalBalanceAmount() > 0;
    }

    /**
     * Check if user has negative balance.
     */
    public function hasNegativeBalance(): bool
    {
        return $this->getTotalBalanceAmount() < 0;
    }

    /**
     * Get recent balance transactions.
     *
     * @param int $limit Number of transactions to retrieve
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentBalanceTransactions(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->balanceTransactions()
            ->with(['order', 'payment'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get balance transaction history with pagination support.
     *
     * @param int $perPage Number of transactions per page
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getBalanceTransactionHistory(int $perPage = 15): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $this->balanceTransactions()
            ->with(['order', 'payment'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get total amount credited to balance.
     */
    public function getTotalBalanceCredits(): int
    {
        $total = $this->balanceTransactions()
            ->where('type', \App\Enums\BalanceTransactionType::CREDIT)
            ->sum('amount');

        return $total / 100;
    }

    /**
     * Get total amount debited from balance.
     */
    public function getTotalBalanceDebits(): int
    {
        $total = $this->balanceTransactions()
            ->where('type', \App\Enums\BalanceTransactionType::DEBIT)
            ->sum('amount');

        return $total;
    }

    /**
     * Get balance formatted for display.
     *
     * @param string $currency Currency symbol
     * @return string
     */
    public function getFormattedBalance(string $currency = '₽'): string
    {
        $balance = $this->getBalance(); // Get balance in major units
        $sign = $balance >= 0 ? '' : '-';
        $absBalance = abs($balance) / 100;

        return $sign . number_format($absBalance, 2, '.', ' ') . ' ' . $currency;
    }


    /**
     * Get the access URL for this user.
     */
    public function getAccessUrlAttribute()
    {
        return \App\Services\HelperService::getCurrentAppUrl() . '/access/' . $this->id;
    }

    /* ---------------- Referral Reward Methods ---------------- */

    /**
     * Реферальные вознаграждения, полученные пользователем (как реферером).
     */
    public function referralRewardsEarned(): HasMany
    {
        return $this->hasMany(ReferralReward::class, 'referrer_user_id');
    }

    /**
     * Реферальные вознаграждения, сгенерированные пользователем (как рефералом).
     */
    public function referralRewardsGenerated(): HasMany
    {
        return $this->hasMany(ReferralReward::class, 'origin_user_id');
    }

    /**
     * Получить общую сумму заработанных реферальных вознаграждений.
     */
    public function getTotalReferralRewardsEarned(): int
    {
        return $this->referralRewardsEarned()->sum('amount');
    }

    /**
     * Получить общую сумму сгенерированных реферальных вознаграждений.
     */
    public function getTotalReferralRewardsGenerated(): int
    {
        return $this->referralRewardsGenerated()->sum('amount');
    }

    /**
     * Получить статистику реферальных вознаграждений по уровням.
     */
    public function getReferralRewardStatsByLevel(): array
    {
        return ReferralReward::getRewardStatsByLevel($this->id);
    }

    /**
     * Получить отформатированную сумму заработанных реферальных вознаграждений.
     */
    public function getFormattedReferralRewardsEarned(string $currency = '₽'): string
    {
        $amount = $this->getTotalReferralRewardsEarned() / 100;
        return number_format($amount, 2, '.', ' ') . ' ' . $currency;
    }

    /**
     * Проверить, есть ли у пользователя реферальные вознаграждения.
     */
    public function hasReferralRewards(): bool
    {
        return $this->referralRewardsEarned()->exists();
    }

    /**
     * Получить последние реферальные вознаграждения.
     */
    public function getRecentReferralRewards(int $limit = 10): Collection
    {
        return $this->referralRewardsEarned()
            ->with(['originUser', 'order'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Generate a unique email for the user.
     */
    public static function generateUniqueEmail(): string
    {
        // <EMAIL>
        $prefix = config('common.xui_client_email_prefix', 'client');
        $domain = config('common.xui_client_email_domain', 'vpn.local');
        $nameLength = config('common.xui_client_email_name_length', 8);

        do {
            $randomString = Str::lower(Str::random($nameLength));
            $email = "{$prefix}-{$randomString}@{$domain}";
        } while (User::where('email', $email)->exists());

        return $email;
    }

    /**
     * Route notifications for the mail channel.
     * Use real_email instead of email for notifications.
     */
    public function routeNotificationForMail(): ?string
    {
        return $this->real_email;
    }

}
