<?php

namespace App\Models;

use App\Services\HelperService;
use App\Services\Traffic\TrafficLoggerService;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class Subscription extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'order_id',
        'plan_id',
        'start_date',
        'end_date',
        'status',
        'is_manual_extension',
        'notes',
        'admin_notes',
        'traffic_used_bytes',
        'traffic_up_bytes',
        'traffic_down_bytes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'is_manual_extension' => 'boolean',
            'traffic_used_bytes' => 'integer',
            'traffic_up_bytes' => 'integer',
            'traffic_down_bytes' => 'integer',
        ];
    }

    protected $appends = [
        'traffic_used_formatted',
        'traffic_detailed',
    ];


    /* ---------------- Relationships ---------------- */

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order that created this subscription.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the subscription plan. (alias for subscriptionPlan())
     */
    public function plan(): BelongsTo
    {
        return $this->subscriptionPlan();
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the subscription history records.
     */
    public function history(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class);
    }

    /**
     * Get the traffic logs for this subscription.
     */
    public function trafficLogs(): HasMany
    {
        return $this->hasMany(UserTrafficLog::class);
    }

    /**
     * Get the access logs for this subscription.
     */
    public function accessLogs(): HasMany
    {
        return $this->hasMany(SubscriptionAccessLog::class);
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only truly active subscriptions (not expired by date).
     */
    public function scopeTrulyActive($query)
    {
        return $query->where('status', 'active')
            ->where(function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
            });
    }

    /**
     * Scope to get only expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Scope to get only cancelled subscriptions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope to get only exceed subscriptions.
     */
    public function scopeExceed($query)
    {
        return $query->where('status', 'exceed');
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Set the autogenerated notes if notes is empty
     */
    public function setNotesAttribute($value)
    {
        if (empty($value)) {
            $value = $this->generateNotes();
        }
        $this->attributes['notes'] = $value;
    }

    public function getTrafficUsedFormattedAttribute()
    {
        if (!$this->user_id) {
            return HelperService::formatTraffic(0);
        }

        // Кэшируем результат на 5 минут для уменьшения нагрузки
        $cacheKey = "subscription_traffic_{$this->id}_{$this->user_id}";
        $trafficBytes = Cache::remember($cacheKey, 300, function () {
            return app(TrafficLoggerService::class)->getTotalTrafficForUser(userId: $this->user_id, subscriptionId: $this->id);
        });

        return HelperService::formatTraffic($trafficBytes);
    }

    /**
     * Get the detailed traffic usage for the subscription.
     */
    public function getTrafficDetailed()
    {
        if (!$this->user_id) {
            return [
                'up' => 0, 'down' => 0, 'total' => 0,
                'up_formatted' => HelperService::formatTraffic(0),
                'down_formatted' => HelperService::formatTraffic(0),
                'total_formatted' => HelperService::formatTraffic(0),
            ];
        }

        // Кэшируем результат на 5 минут
        $cacheKey = "subscription_traffic_detailed_{$this->id}_{$this->user_id}";
        return Cache::remember($cacheKey, 300, function () {
            $traffic = app(TrafficLoggerService::class)->getDetailedTrafficForUser(userId: $this->user_id, subscriptionId: $this->id);
            $traffic['up_formatted'] = HelperService::formatTraffic($traffic['up']);
            $traffic['down_formatted'] = HelperService::formatTraffic($traffic['down']);
            $traffic['total_formatted'] = HelperService::formatTraffic($traffic['total']);
            return $traffic;
        });
    }

    /**
     * Get the detailed traffic usage for the subscription.
     */
    public function getTrafficDetailedAttribute()
    {
        return $this->getTrafficDetailed();
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               (!$this->end_date || $this->end_date->isFuture());
    }

    /**
     * Check if the subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->end_date && $this->end_date->isPast());
    }

    /**
     * Check if the subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the subscription is demo.
     */
    public function isDemo(): bool
    {
        return $this->plan && $this->plan->isDemo();
    }

    /**
     * Check if the subscription is about to expire.
     */
    public function isAboutToExpire(int $days = 3): bool
    {
        if (! $this->end_date || ! $this->isActive() || $this->isExpired()) {
            return false;
        }

        return now()->diffInDays($this->end_date) <= $days;
    }

    /**
     * Check if renewal is required (expired or about to expire).
     */
    public function isRenewalRequired(): bool
    {
        return ! $this->isActive() || $this->isExpired() || $this->isAboutToExpire();
    }

    /**
     * Check if traffic limit is exceeded.
     */
    public function isTrafficExceeded(): bool
    {
        // if traffic_limit_bytes is null, then there is no limit - plan is unlimited for traffic
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return false;
        }

        $trafficLoggerService = app(TrafficLoggerService::class);
        return $trafficLoggerService->isTrafficLimitExceededForSubscription($this);
    }


    /* ---------------- Actions ---------------- */

    /**
     * Cancel the subscription.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    /**
     * Expire the subscription.
     */
    public function expire(): void
    {
        $this->update([
            'status' => 'expired',
        ]);
    }

    /**
     * Mark the subscription as exceeded (traffic limit exceeded).
     */
    public function exceed(): void
    {
        $this->update([
            'status' => 'exceed',
        ]);
    }

    /**
     * Renew the subscription to a new end date.
     */
    public function renewTo(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'status' => 'active',
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Extend the subscription to a new end date.
     */
    public function extendTo(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'status' => 'active',
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Change the end date of the subscription.
     */
    public function changeEndDate(?Carbon $newEndDate): void
    {
        $this->update([
            'end_date' => $newEndDate,
            'notes' => $this->generateNotes(),
        ]);
    }

    /**
     * Upgrade the subscription to a new plan.
     */
    public function changePlan(SubscriptionPlan $newPlan, ?Carbon $newEndDate): void
    {
        $this->update([
            'plan_id' => $newPlan->id,
            'end_date' => $newEndDate,
            'notes' => $this->generateNotes(),
        ]);
    }

    public function changeOrderTo(Order $order): void
    {
        $this->bindToOrder($order);
    }

    /**
     * Bind the subscription to an order.
     */
    public function bindToOrder(Order $order): void
    {
        if ($this->order_id === $order->id) {
            return;
        }

        $this->update([
            'order_id' => $order->id,
        ]);
    }

    /**
     * Update the traffic usage for the subscription.
     */
    public function updateTrafficUsage(int $downBytes, int $upBytes)
    {
        $totalBytes = $downBytes + $upBytes;

        $this->traffic_used_bytes = $totalBytes;
        $this->traffic_up_bytes = $upBytes;
        $this->traffic_down_bytes = $downBytes;
        return $this->save();

        // $this->update([
        //     'traffic_used_bytes' => $totalBytes,
        //     'traffic_up_bytes' => $upBytes,
        //     'traffic_down_bytes' => $downBytes,
        // ]);
    }

    public function changeNotes(?string $notes): void
    {
        if ($notes === null) {
            return;
        }

        $this->update([
            'notes' => $notes,
        ]);
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Get the expiry time in milliseconds. For XUI server client.
     */
    public function getExpiryTimeMs(): int
    {
        return $this->end_date ? $this->end_date->getTimestampMs() : 0;
    }

    /**
     * Get the remaining days until expiration.
     */
    public function getRemainingDays(): ?int
    {
        if (!$this->end_date) {
            return null;
        }
        return max(0, now()->diffInDays($this->end_date));
    }

    /**
     * Get the remaining time until expiration.
     */
    public function getRemainingTime(?string $locale = 'ru'): string
    {
        if (!$this->end_date) {
            return '';
        }
        return $this->end_date?->locale($locale)->diffForHumans(now(), CarbonInterface::DIFF_ABSOLUTE);
        // return now()->locale($locale)->diffForHumans($this->end_date);
    }

    /**
     * Get the traffic usage percentage.
     */
    public function getTrafficUsagePercentage(): ?float
    {
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return null;
        }
        return min(100, ($this->traffic_used_bytes / $this->plan->traffic_limit_bytes) * 100);
    }

    /**
     * Get the remaining traffic in bytes.
     */
    public function getRemainingTrafficBytes(): ?int
    {
        if (!$this->plan || !$this->plan->traffic_limit_bytes) {
            return null;
        }
        return max(0, $this->plan->traffic_limit_bytes - $this->traffic_used_bytes);
    }

    /**
     * Get the formatted price based on plan's price and currency
     */
    public function toFormattedPrice(): string
    {
        return number_format($this->plan->price / 100, 2) . ' ' . $this->plan->currency;
    }

    /**
     * Generate notes for the subscription.
     */
    public function generateNotes(?SubscriptionPlan $plan = null): string
    {
        $plan = $plan ?? $this->plan;

        // notes from plan name, duration, and price
        return 'Subscription for '
            . $plan->name . ' for '
            . $plan->duration . ' ' . $plan->duration_unit
            . ' at a cost of ' . $plan->toFormattedPrice();
    }

}
