<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearTrafficCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-traffic {--subscription_id= : Clear cache for specific subscription}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear traffic cache for subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $subscriptionId = $this->option('subscription_id');

        if ($subscriptionId) {
            // Очистить кэш для конкретной подписки
            $this->clearSubscriptionCache($subscriptionId);
            $this->info("Traffic cache cleared for subscription: {$subscriptionId}");
        } else {
            // Очистить весь кэш трафика
            $this->clearAllTrafficCache();
            $this->info('All traffic cache cleared');
        }
    }

    /**
     * Clear cache for specific subscription
     */
    private function clearSubscriptionCache(string $subscriptionId): void
    {
        // Получаем подписку для получения user_id
        $subscription = \App\Models\Subscription::find($subscriptionId);
        
        if ($subscription) {
            $cacheKey1 = "subscription_traffic_{$subscription->id}_{$subscription->user_id}";
            $cacheKey2 = "subscription_traffic_detailed_{$subscription->id}_{$subscription->user_id}";
            
            Cache::forget($cacheKey1);
            Cache::forget($cacheKey2);
        }
    }

    /**
     * Clear all traffic cache
     */
    private function clearAllTrafficCache(): void
    {
        // Получаем все ключи кэша трафика и удаляем их
        $keys = Cache::getRedis()->keys('*subscription_traffic*');
        
        if (!empty($keys)) {
            foreach ($keys as $key) {
                // Убираем префикс Redis если он есть
                $cleanKey = str_replace(config('cache.prefix') . ':', '', $key);
                Cache::forget($cleanKey);
            }
        }
    }
}
