<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Telescope::night();

        $this->hideSensitiveRequestDetails();

        $isLocal = $this->app->environment('local');

        Telescope::filter(function (IncomingEntry $entry) use ($isLocal) {
            // В локальной среде записываем всё
            if ($isLocal) {
                return true;
            }

            // В продакшн записываем больше данных для мониторинга
            return $entry->isReportableException() ||
                   $entry->isFailedRequest() ||
                   $entry->isFailedJob() ||
                   $entry->isScheduledTask() ||
                   $entry->hasMonitoredTag() ||
                   $entry->type === 'request' ||  // Все HTTP запросы
                   $entry->type === 'query' ||   // SQL запросы
                   $entry->type === 'command' || // Artisan команды
                   $entry->type === 'job' ||     // Все задачи
                   $entry->type === 'log';       // Логи
        });
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     */
    protected function hideSensitiveRequestDetails(): void
    {
        if ($this->app->environment('local')) {
            return;
        }

        Telescope::hideRequestParameters(['_token']);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
        ]);
    }

    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     */
    protected function gate(): void
    {
        Gate::define('viewTelescope', function () {
            // В локальной среде разрешаем всем
            if ($this->app->environment('local')) {
                return true;
            }

            // Проверяем авторизацию через сессию
            return session('telescope_auth', false);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->gate();
    }
}
