<?php

namespace App\Services\Traffic;

use App\DTOs\Xui\ClientStatDTO;
use App\DTOs\Xui\InboundDTO;
use App\DTOs\Traffic\TrafficLogDTO;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserTrafficLog;
use App\Models\XuiServer;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class TrafficLoggerService
{
    /**
     * Логирует трафик пользователя на основании DTO
     *
     * @param TrafficLogDTO $dto
     * @return UserTrafficLog
     */
    public function log(TrafficLogDTO $dto): ?UserTrafficLog
    {
        $lastLog = UserTrafficLog::where('user_id', $dto->user_id)
            ->where('xui_server_id', $dto->xui_server_id)
            ->where('subscription_id', $dto->subscription_id)
            ->where('inbound_id', $dto->inbound_id)
            ->latest('data_as_of_timestamp')
            ->first();

        $currentTimestamp = $dto->data_as_of_timestamp ?? now();

        // Если запись уже есть и она новее или такая же — пропускаем
        if ($lastLog && $lastLog->data_as_of_timestamp >= $currentTimestamp) {
            return null;
        }

        // Проверка: был ли трафик?
        $hasTrafficIncreased = !$lastLog || (
            $dto->traffic_up_bytes != $lastLog->traffic_up_bytes ||
            $dto->traffic_down_bytes != $lastLog->traffic_down_bytes
        );

        // Проверка: прошёл ли "heartbeat-интервал"?
        //
        // 🔹 Что такое heartbeat-интервал?
        // Это минимальное время (в минутах), которое должно пройти с последнего лога,
        // чтобы повторно записать лог даже при отсутствии изменений трафика.
        //
        // 🔹 Зачем он нужен?
        // Чтобы отслеживать "живость" клиента — фиксировать, что он всё ещё онлайн,
        // даже если трафик не изменился (например, просто пингует).
        //
        // 🔹 Почему это важно?
        // Без heartbeat логирования клиенты без активности могут "исчезать" из логов,
        // особенно если они онлайн, но ничего не качают/не отправляют.
        //
        // Если с последнего лога прошло ≥ 15 минут — разрешаем запись (heartbeat).
        $heartbeatIntervalMinutes = 15;
        $hasHeartbeatExpired = !$lastLog || $lastLog->data_as_of_timestamp->diffInMinutes($currentTimestamp) >= $heartbeatIntervalMinutes;

        // Если трафика не было и интервал не истёк — не логируем
        if (!$hasTrafficIncreased && !$hasHeartbeatExpired) {
            return null;
        }

        // Определение сброса трафика
        $isReset = false;
        $nextResetSeq = $lastLog ? $lastLog->reset_sequence : 1;

        if ($lastLog && (
            $dto->traffic_up_bytes < $lastLog->traffic_up_bytes ||
            $dto->traffic_down_bytes < $lastLog->traffic_down_bytes
        )) {
            $isReset = true;
            $nextResetSeq++;
        }

        return UserTrafficLog::create([
            'user_id' => $dto->user_id,
            'subscription_id' => $dto->subscription_id,
            'xui_server_id' => $dto->xui_server_id,
            'inbound_id' => $dto->inbound_id,
            'data_as_of_timestamp' => $currentTimestamp,
            'traffic_up_bytes' => $dto->traffic_up_bytes,
            'traffic_down_bytes' => $dto->traffic_down_bytes,
            'traffic_used_bytes' => $dto->traffic_up_bytes + $dto->traffic_down_bytes,
            'is_reset' => $isReset,
            'reset_sequence' => $nextResetSeq,
            'created_at' => $currentTimestamp,
            'updated_at' => $currentTimestamp,
        ]);
    }

    /**
     * Логирует трафик по одному клиенту из ClientStatDTO
     *
     * @param ClientStatDTO $dto
     * @param XuiServer $server
     * @param Carbon|null $dataAsOfTimestamp
     * @return UserTrafficLog|null
     */
    public function logFromClientStatDTO(ClientStatDTO $dto, XuiServer $server, ?Carbon $dataAsOfTimestamp = null): ?UserTrafficLog
    {
        $email = User::extractEmailFromClientEmail($dto->email, $dto->inboundId);
        $user = User::where('email', $email)->first();

        if (!$user) {
            return null;
        }

        $trafficDto = new TrafficLogDTO([
            'user_id' => $user->id,
            'subscription_id' => $user->currentSubscription?->id,
            'xui_server_id' => $server->id,
            'inbound_id' => $dto->inboundId,
            'traffic_up_bytes' => $dto->up,
            'traffic_down_bytes' => $dto->down,
            'data_as_of_timestamp' => $dataAsOfTimestamp ?? $server->raw_inbounds_list_updated_at ?? now(),
        ]);

        return $this->log($trafficDto);
    }

    /**
     * Логирует трафик по массиву ClientStatDTO
     *
     * @param ClientStatDTO[] $dtos
     * @param XuiServer $server
     * @param Carbon|null $dataAsOfTimestamp
     * @return void
     */
    public function logFromClientStatDTOs(array $dtos, XuiServer $server, ?Carbon $dataAsOfTimestamp = null): void
    {
        foreach ($dtos as $dto) {
            $this->logFromClientStatDTO($dto, $server, $dataAsOfTimestamp);
        }
    }

    /**
     * Логирует трафик по всем клиентам одного inbound
     *
     * @param InboundDTO $inbound
     * @param XuiServer $server
     * @return void
     */
    public function logFromInboundDTO(InboundDTO $inbound, XuiServer $server, ?Carbon $dataAsOfTimestamp = null): void
    {
        foreach ($inbound->clientsStats() as $dto) {
            $this->logFromClientStatDTO($dto, $server, $dataAsOfTimestamp);
        }
    }

    /**
     * Логирует трафик по всем inbound-ам XUI-сервера
     *
     * @param XuiServer $server
     * @return void
     */
    public function logFromXuiServer(XuiServer $server): void
    {
        foreach ($server->inbounds() as $inboundDTO) {
            $this->logFromInboundDTO($inboundDTO, $server, $server->raw_inbounds_list_updated_at ?? null);
        }
    }

    /**
     * Получить суммарный трафик подробно входящий/исходящий/суммарный (с учётом сбросов)
     *
     * @param string $userId
     * @param Carbon|null $from
     * @param Carbon|null $to
     * @param string|null $subscriptionId
     * @param string|null $serverId
     * @return array{up: int, down: int, total: int}
     */
    public function getDetailedTrafficForUser(
        string $userId,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?string $subscriptionId = null,
        ?string $serverId = null,
    ): array {
        $query = UserTrafficLog::query()
            ->select(['traffic_up_bytes', 'traffic_down_bytes', 'traffic_used_bytes', 'is_reset', 'reset_sequence', 'data_as_of_timestamp'])
            ->where('user_id', $userId);

        if ($from) $query->where('data_as_of_timestamp', '>=', $from);
        if ($to) $query->where('data_as_of_timestamp', '<=', $to);
        if ($subscriptionId) $query->where('subscription_id', $subscriptionId);
        if ($serverId) $query->where('xui_server_id', $serverId);

        $logs = $query->get();

        return $this->calculateDetailedTrafficConsideringResets($logs);
    }

    /**
     * Считает трафик (входящий, исходящий, суммарный) с учётом сбросов
     *
     * @param Collection<UserTrafficLog> $logs
     * @return array{up: int, down: int, total: int}
     */
    public function calculateDetailedTrafficConsideringResets(Collection $logs): array
    {
        $grouped = $logs->groupBy(fn($log) => "{$log->reset_sequence}_{$log->inbound_id}");

        $totalUp = 0;
        $totalDown = 0;

        foreach ($grouped as $group) {
            $sorted = $group->sortBy('data_as_of_timestamp');
            $first = $sorted->first();
            $last = $sorted->last();

            // Входящий и исходящий трафик
            $up = max(0, $last->traffic_up_bytes - $first->traffic_up_bytes);
            $down = max(0, $last->traffic_down_bytes - $first->traffic_down_bytes);

            $totalUp += $up;
            $totalDown += $down;
        }

        return [
            'up' => $totalUp,
            'down' => $totalDown,
            'total' => $totalUp + $totalDown,
        ];
    }

    /**
     * Получить суммарный трафик за период (с учётом сбросов)
     *
     * @param string $userId
     * @param Carbon|null $from
     * @param Carbon|null $to
     * @param string|null $subscriptionId
     * @param string|null $serverId
     * @return int
     */
    public function getTotalTrafficForUser(
        string $userId,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?string $subscriptionId = null,
        ?string $serverId = null,
    ): int {
        $query = UserTrafficLog::query()
            ->select(['traffic_used_bytes', 'is_reset', 'reset_sequence', 'data_as_of_timestamp'])
            ->where('user_id', $userId);

        if ($from) $query->where('data_as_of_timestamp', '>=', $from);
        if ($to) $query->where('data_as_of_timestamp', '<=', $to);
        if ($subscriptionId) $query->where('subscription_id', $subscriptionId);
        if ($serverId) $query->where('xui_server_id', $serverId);

        $logs = $query->get();

        return $this->calculateTrafficConsideringResets($logs);
    }

    /**
     * Считает трафик с учётом сбросов счётчиков
     *
     * @param Collection<UserTrafficLog> $logs
     * @return int
     */
    public function calculateTrafficConsideringResets(Collection $logs): int
    {
        return $logs
            ->groupBy(fn($log) => "{$log->reset_sequence}_{$log->inbound_id}")
            ->map(function (Collection $group) {
                $sorted = $group->sortBy('data_as_of_timestamp');
                $first = $sorted->first();
                $last = $sorted->last();

                return max(0, $last->traffic_used_bytes - $first->traffic_used_bytes);
            })
            ->sum();
    }

    /**
     * Получить трафик пользователя, сгруппированный по подпискам
     *
     * @param string $userId
     * @param Carbon $from
     * @param Carbon $to
     * @return Collection<string, int> subscription_id => traffic_bytes
     */
    public function getUserTrafficGroupedBySubscription(string $userId, Carbon $from, Carbon $to): Collection
    {
        $logs = UserTrafficLog::where('user_id', $userId)
            ->whereBetween('data_as_of_timestamp', [$from, $to])
            ->get();

        return $logs
            ->groupBy('subscription_id')
            ->map(fn(Collection $subscriptionLogs) => $this->calculateTrafficConsideringResets($subscriptionLogs));
    }

    /**
     * Получить трафик по пользователям за указанный период
     *
     * @param Carbon $from
     * @param Carbon $to
     * @return Collection<string, int> user_id => traffic_bytes
     */
    public function getTotalTrafficPerUser(Carbon $from, Carbon $to): Collection
    {
        $logs = UserTrafficLog::whereBetween('data_as_of_timestamp', [$from, $to])->get();

        return $logs
            ->groupBy('user_id')
            ->map(fn(Collection $userLogs) => $this->calculateTrafficConsideringResets($userLogs));
    }

    /**
     * Получить информацию превышен ли лимит трафика пользователя по тарифу
     *
     * @param User $user
     * @param ?Carbon $from
     * @param ?Carbon $to
     * @return bool
     */
    public function isTrafficLimitExceededForUser(User $user, ?Carbon $from = null, ?Carbon $to = null): bool
    {
        if (!$user->currentSubscription || !$user->currentSubscription->plan) {
            return false;
        }

        $totalTraffic = $this->getTotalTrafficForUser($user->id, $from, $to, $user->currentSubscription->id);
        return $totalTraffic > $user->currentSubscription->plan->traffic_limit_bytes;
    }

    /**
     * Получить информацию превышен ли лимит трафика пользователя по подписке
     *
     * @param Subscription $subscription
     * @param ?Carbon $from
     * @param ?Carbon $to
     * @return bool
     */
    public function isTrafficLimitExceededForSubscription(Subscription $subscription, ?Carbon $from = null, ?Carbon $to = null): bool
    {
        if (!$subscription->plan || !$subscription->plan->traffic_limit_bytes) {
            return false;
        }

        $totalTraffic = $this->getTotalTrafficForUser($subscription->user->id, $from, $to, $subscription->id);
        return $totalTraffic >= $subscription->plan->traffic_limit_bytes;
    }

    /**
     * Проверить приближается ли трафик к лимиту (по умолчанию 80% от лимита)
     *
     * @param Subscription $subscription
     * @param float $threshold Порог в процентах (0.8 = 80%)
     * @param ?Carbon $from
     * @param ?Carbon $to
     * @return bool
     */
    public function isTrafficApproachingLimit(Subscription $subscription, float $threshold = 0.8, ?Carbon $from = null, ?Carbon $to = null): bool
    {
        if (!$subscription->plan || !$subscription->plan->traffic_limit_bytes) {
            return false;
        }

        $totalTraffic = $this->getTotalTrafficForUser($subscription->user->id, $from, $to, $subscription->id);
        $limitBytes = $subscription->plan->traffic_limit_bytes;

        return $totalTraffic >= ($limitBytes * $threshold);
    }

    /**
     * Получить процент использования трафика для подписки
     *
     * @param Subscription $subscription
     * @param ?Carbon $from
     * @param ?Carbon $to
     * @return float|null
     */
    public function getTrafficUsagePercentage(Subscription $subscription, ?Carbon $from = null, ?Carbon $to = null): ?float
    {
        if (!$subscription->plan || !$subscription->plan->traffic_limit_bytes) {
            return null;
        }

        $totalTraffic = $this->getTotalTrafficForUser($subscription->user->id, $from, $to, $subscription->id);
        return min(100, ($totalTraffic / $subscription->plan->traffic_limit_bytes) * 100);
    }

    /**
     * Получить трафик для множества подписок одним запросом
     *
     * @param array $subscriptionIds
     * @param ?Carbon $from
     * @param ?Carbon $to
     * @return array subscription_id => traffic_bytes
     */
    public function getBulkTrafficForSubscriptions(array $subscriptionIds, ?Carbon $from = null, ?Carbon $to = null): array
    {
        if (empty($subscriptionIds)) {
            return [];
        }

        $query = UserTrafficLog::query()
            ->select(['subscription_id', 'traffic_used_bytes', 'is_reset', 'reset_sequence'])
            ->whereIn('subscription_id', $subscriptionIds);

        if ($from) $query->where('data_as_of_timestamp', '>=', $from);
        if ($to) $query->where('data_as_of_timestamp', '<=', $to);

        $logs = $query->get()->groupBy('subscription_id');

        $result = [];
        foreach ($subscriptionIds as $subscriptionId) {
            $subscriptionLogs = $logs->get($subscriptionId, collect());
            $result[$subscriptionId] = $this->calculateTrafficConsideringResets($subscriptionLogs);
        }

        return $result;
    }

}
