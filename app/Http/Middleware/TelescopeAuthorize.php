<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TelescopeAuthorize
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // В локальной среде пропускаем все
        if (app()->environment('local')) {
            return $next($request);
        }

        // ВАЖНО: Пропускаем маршруты авторизации, чтобы избежать циклов редиректов
        if ($request->is('telescope/login') || $request->is('telescope/logout')) {
            return $next($request);
        }

        // Проверяем авторизацию через сессию
        if (session('telescope_auth')) {
            return $next($request);
        }

        // Если не авторизован, перенаправляем на страницу логина
        return redirect('/telescope/login');
    }
}
