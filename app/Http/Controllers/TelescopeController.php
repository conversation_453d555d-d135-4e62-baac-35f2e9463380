<?php

namespace App\Http\Controllers;

use App\Models\AdminUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class TelescopeController extends Controller
{
    /**
     * Show Telescope login page.
     */
    public function showLogin()
    {
        // Если уже авторизован, перенаправляем в Telescope
        if (Session::get('telescope_auth')) {
            return redirect('/telescope');
        }

        return view('telescope-login');
    }

    /**
     * Handle Telescope login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $username = $request->input('username');
        $password = $request->input('password');

        // Ищем админа в базе данных
        $admin = AdminUser::where('username', $username)
            ->where('is_active', true)
            ->first();

        if ($admin && Hash::check($password, $admin->password)) {
            // Сохраняем авторизацию в сессии
            Session::put('telescope_auth', true);
            Session::put('telescope_user', $admin->username);
            
            return redirect('/telescope');
        }

        return back()->withErrors([
            'login' => 'Неверные учетные данные или пользователь неактивен.',
        ])->withInput($request->only('username'));
    }

    /**
     * Handle Telescope logout.
     */
    public function logout()
    {
        Session::forget('telescope_auth');
        Session::forget('telescope_user');
        
        return redirect('/telescope/login');
    }
}
