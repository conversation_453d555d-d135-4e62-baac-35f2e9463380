<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreSubscriptionRequest;
use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use App\Models\PaymentMethod;
use App\Services\HelperService;
use App\Services\Traffic\TrafficLoggerService;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;

class AdminSubscriptionController extends Controller
{
    public function __construct(
        private TrafficLoggerService $trafficLogger,
        private OrderService $orderService,
        private PaymentService $paymentService,
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Display a listing of subscriptions.
     */
    public function index(Request $request): JsonResponse
    {
        $expiringSoonDays = config('common.expiring_soon_days', 3);

        try {
            $perPage = (int) $request->get('per_page', 20);
            $search = $request->get('search');
            $status = $request->get('status');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');

            $query = Subscription::with(['user', 'plan', 'order']);

            // Поиск
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('email', 'like', "%{$search}%")
                            ->orWhere('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('plan', function ($planQuery) use ($search) {
                        $planQuery->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('order', function ($orderQuery) use ($search) {
                        $orderQuery->where('public_id', 'like', "%{$search}%");
                    });
                });
            }

            // Фильтрация по статусу
            if ($status) {
                switch ($status) {
                    case 'active':
                        $query->where('status', 'active')
                            ->where(function ($q) {
                                $q->whereNull('end_date')
                                    ->orWhere('end_date', '>', now());
                            });
                        break;
                    case 'expiring_soon':
                        $query->where('status', 'active')
                            ->whereNotNull('end_date')
                            ->whereBetween('end_date', [now(), now()->addDays($expiringSoonDays)]);
                        break;
                    case 'expired':
                        $query->where(function ($q) {
                            $q->where('status', 'expired')
                                ->orWhere(function ($subQ) {
                                    $subQ->where('status', 'active')
                                        ->whereNotNull('end_date')
                                        ->where('end_date', '<=', now());
                                });
                        });
                        break;
                    case 'cancelled':
                        $query->where('status', 'cancelled');
                        break;

                    case 'demo':
                        $query->where('status', 'active')
                                ->whereHas('plan', function ($q) {
                                    $q->where('is_demo', true);
                                });
                        break;
                }
            }

            // Сортировка
            $allowedSortFields = ['created_at', 'start_date', 'end_date', 'status'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortDirection);
            }

            $subscriptions = $query->paginate($perPage);

            // Добавляем дополнительные данные для каждой подписки
            $subscriptions->getCollection()->transform(function ($subscription) use ($expiringSoonDays) {
                // Форматируем лимит трафика
                $subscription->traffic_limit_formatted = $subscription?->plan?->traffic_limit_bytes
                    ? $this->formatBytes($subscription->plan->traffic_limit_bytes)
                    : '∞';

                // Время до истечения
                if ($subscription->end_date) {
                    $now = now();
                    $endDate = Carbon::parse($subscription->end_date);

                    if ($endDate->isPast()) {
                        $subscription->expires_in = 'Истекла ' . $endDate->diffForHumans($now);
                        $subscription->expires_in_short = 'Истекла';
                        $subscription->expires_in_detailed = 'Истекла ' . $endDate->diffForHumans($now);
                        $subscription->expires_status = 'expired';
                    } elseif ($endDate->isBetween($now, $now->copy()->addDays($expiringSoonDays))) {
                        $subscription->expires_in = 'Осталось ' . HelperService::formatRemainingTimeShort($endDate);
                        $subscription->expires_in_short = 'Осталось ' . HelperService::formatRemainingTimeShort($endDate);
                        $subscription->expires_in_detailed = 'Осталось ' . HelperService::formatRemainingTimeDetailed($endDate);
                        $subscription->expires_status = 'expiring_soon';
                    } else {
                        $subscription->expires_in = 'Осталось ' . HelperService::formatRemainingTimeShort($endDate);
                        $subscription->expires_in_short = 'Осталось ' . HelperService::formatRemainingTimeShort($endDate);
                        $subscription->expires_in_detailed = 'Осталось ' . HelperService::formatRemainingTimeDetailed($endDate);
                        $subscription->expires_status = 'active';
                    }
                } else {
                    $subscription->expires_in = 'Бессрочная';
                    $subscription->expires_in_short = 'Бессрочная';
                    $subscription->expires_in_detailed = 'Бессрочная';
                    $subscription->expires_status = 'unlimited';
                }

                // Получаем статистику трафика
                $trafficBytes = 0;
                if ($subscription->user_id) {
                    $subscription->traffic_used_bytes = $trafficBytes;
                    $subscriptionTraffic = $subscription->trafficDetailed;
                    $trafficBytes = $subscriptionTraffic['total_formatted'];
                    $subscription->traffic_up_formatted = $subscriptionTraffic['up_formatted'];
                    $subscription->traffic_down_formatted = $subscriptionTraffic['down_formatted'];
                }

                // Серверные пулы (пока пустой массив, добавим позже)
                $subscription->server_pools = $subscription?->user?->activeServerPools
                                    ->load('activeServers') // eager load
                                    ->map(fn ($pool) => [
                                        'id' => $pool->id,
                                        'name' => $pool->name,
                                        'servers' => $pool->activeServers->map(fn ($server) => [
                                            'id' => $server->id,
                                            'name' => $server->name,
                                            'address' => $server->address,
                                        ])->values(),
                                    ])->values();

                return $subscription;
            });

            return response()->json([
                'success' => true,
                'data' => $subscriptions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get subscription statistics for tabs.
     */
    public function getStats(): JsonResponse
    {
        $now = now();
        $expiringSoonDays = config('common.expiring_soon_days', 3);
        $expiringSoonDate = $now->copy()->addDays($expiringSoonDays);

        // Оптимизированный запрос с одним обращением к БД
        $stats = DB::select("
            SELECT
                SUM(CASE
                    WHEN status = 'active' AND (end_date IS NULL OR end_date > ?)
                    THEN 1 ELSE 0
                END) as active,
                SUM(CASE
                    WHEN status = 'active' AND end_date IS NOT NULL
                         AND end_date BETWEEN ? AND ?
                    THEN 1 ELSE 0
                END) as expiring_soon,
                SUM(CASE
                    WHEN status = 'expired' OR
                         (status = 'active' AND end_date IS NOT NULL AND end_date <= ?)
                    THEN 1 ELSE 0
                END) as expired,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
            FROM subscriptions
            WHERE deleted_at IS NULL
        ", [$now, $now, $expiringSoonDate, $now]);

        // Отдельный запрос для demo подписок с JOIN
        $demoCount = DB::select("
            SELECT COUNT(*) as demo_count
            FROM subscriptions s
            INNER JOIN subscription_plans sp ON s.plan_id = sp.id
            WHERE s.deleted_at IS NULL
              AND sp.deleted_at IS NULL
              AND sp.is_demo = 1
              AND sp.is_active = 1
        ");

        $result = [
            'active' => (int) $stats[0]->active,
            'expiring_soon' => (int) $stats[0]->expiring_soon,
            'expired' => (int) $stats[0]->expired,
            'cancelled' => (int) $stats[0]->cancelled,
            'demo' => (int) $demoCount[0]->demo_count,
        ];

        return response()->json([
            'success' => true,
            'data' => $result,
        ]);
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription): JsonResponse
    {
        $subscription->load([
            'user',
            'plan',
            'order',
            'history',
            'user.serverAssignments.serverPool.servers',
        ]);

        return response()->json([
            'success' => true,
            'data' => $subscription,
        ]);
    }

    /**
     * Create a new subscription.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Определяем правила валидации в зависимости от того, создаем ли нового пользователя
            $createUser = $request->boolean('create_user');

            $rules = [
                'plan_id' => 'required|exists:subscription_plans,id',
                'payment_method' => 'required|in:free,balance,cash,manual',
                'start_date' => 'nullable|date',
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
                'create_user' => 'nullable|boolean',
            ];

            if ($createUser) {
                // Если создаем нового пользователя
                $rules['user_name'] = 'required|string|max:255';
                $rules['user_email'] = 'nullable|email|unique:users,email';
                $rules['user_description'] = 'nullable|string|max:1000';
            } else {
                // Если выбираем существующего пользователя
                $rules['user_id'] = 'required|exists:users,id';
            }

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            return DB::transaction(function () use ($request) {
                // Получаем или создаем пользователя
                if ($request->boolean('create_user')) {
                    $user = User::create([
                        'name' => $request->input('user_name'),
                        'email' => $request->input('user_email'),
                        'notes' => $request->input('user_description'),
                        'currency' => 'RUB',
                        'is_active' => true,
                        'registered_at' => now(),
                    ]);
                } else {
                    $user = User::findOrFail($request->input('user_id'));
                }

                // Получаем тарифный план
                $plan = SubscriptionPlan::findOrFail($request->input('plan_id'));

                // Создаем заказ
                $order = $this->orderService->createVpnSubscriptionOrder($user, $plan);

                // Создаем платеж
                $paymentResult = $this->paymentService->createPayment(
                    $order,
                    $request->input('payment_method')
                );

                if (!$paymentResult->success) {
                    throw new \Exception('Failed to create payment: ' . $paymentResult->message);
                }

                $subscription = $this->subscriptionService->manualCreateSubscription(
                    user: $user,
                    plan: $plan,
                    order: $order,
                    startDate: $request->input('start_date') ?? now(),
                    endDate: null,
                    notes: $request->input('notes'),
                    adminNotes: $request->input('admin_notes'),
                );

                // Загружаем связанные данные
                $subscription->load(['user', 'plan', 'order']);

                return response()->json([
                    'success' => true,
                    'data' => $subscription,
                    'message' => 'Подписка успешно создана',
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get users for selection.
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $search = $request->get('search', '');
            $limit = min((int) $request->get('limit', 20), 100);

            $query = User::query()
                ->select(['id', 'name', 'email', 'is_active', 'created_at', 'old_client_id', 'currency'])
                ->with(['currentSubscription.plan:id,name,duration,duration_unit,price,currency'])
                ->where('is_active', true);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('id', 'like', "%{$search}%");
                });
            }

            $users = $query->orderBy('name')
                ->limit($limit)
                ->get()
                ->map(function ($user) {
                    $currentSubscription = $user->currentSubscription;

                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'is_active' => $user->is_active,
                        'created_at' => $user->created_at,
                        'client_id' => $user->client_id,
                        'balance' => $user->balance,
                        'balance_formatted' => $user->balance_formatted,
                        'active_subscription' => $currentSubscription ? [
                            'id' => $currentSubscription->id,
                            'plan_name' => $currentSubscription->plan->name,
                            'end_date' => $currentSubscription->end_date,
                            'end_date_formatted' => $currentSubscription->end_date
                                ? $currentSubscription->end_date->format('d.m.Y H:i')
                                : 'Бессрочно',
                            'status' => $currentSubscription->status,
                        ] : null,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $users,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment methods for selection.
     */
    public function getPaymentMethods(): JsonResponse
    {
        try {
            $methods = PaymentMethod::where('is_active', true)
                ->whereIn('code', ['free', 'balance', 'cash', 'manual'])
                ->select(['id', 'code', 'name', 'description', 'type', 'notes_for_admins'])
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $methods,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Deactivate subscription (cancel)
     */
    public function deactivate(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $subscription = $this->subscriptionService->manualCancelSubscription(
                $subscription,
                $request->input('notes'),
                $request->input('admin_notes')
            );

            return response()->json([
                'success' => true,
                'message' => 'Подписка деактивирована',
                'data' => $subscription,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Extend subscription with payment
     */
    public function extend(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'duration' => 'required|integer|min:1',
                'duration_unit' => 'required|in:day,week,month,year',
                'payment_method' => 'required|in:free,balance,cash,manual',
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors(),
                ], 422);
            }

            return DB::transaction(function () use ($request, $subscription) {
                // Создаем заказ для продления
                $order = $this->orderService->createVpnSubscriptionOrder(
                    $subscription->user,
                    $subscription->plan,
                    'extend'
                );

                // Создаем платеж
                $paymentResult = $this->paymentService->createPayment(
                    $order,
                    $request->input('payment_method')
                );

                if (!$paymentResult->isSuccess()) {
                    throw new \Exception($paymentResult->message ?? 'Ошибка создания платежа');
                }

                // Продлеваем подписку
                $subscription = $this->subscriptionService->manualExtendSubscription(
                    $subscription,
                    $order,
                    $request->input('duration'),
                    $request->input('duration_unit'),
                    $request->input('notes'),
                    $request->input('admin_notes')
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Подписка продлена',
                    'data' => $subscription,
                    'payment_url' => $paymentResult->paymentUrl,
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Renew subscription (same plan)
     */
    public function renew(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|in:free,balance,cash,manual',
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors(),
                ], 422);
            }

            return DB::transaction(function () use ($request, $subscription) {
                // Создаем заказ для продления
                $order = $this->orderService->createVpnSubscriptionOrder(
                    $subscription->user,
                    $subscription->plan,
                    'renew'
                );

                // Создаем платеж
                $paymentResult = $this->paymentService->createPayment(
                    $order,
                    $request->input('payment_method')
                );

                if (!$paymentResult->isSuccess()) {
                    throw new \Exception($paymentResult->message ?? 'Ошибка создания платежа');
                }

                // Продлеваем подписку
                $subscription = $this->subscriptionService->manualRenewSubscription(
                    $subscription,
                    $order,
                    $request->input('notes'),
                    $request->input('admin_notes')
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Подписка продлена',
                    'data' => $subscription,
                    'payment_url' => $paymentResult->paymentUrl,
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Change subscription plan
     */
    public function changePlan(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'new_plan_id' => 'required|exists:subscription_plans,id',
                'recalculate_days' => 'boolean',
                'payment_method' => 'required|in:free,balance,cash,manual',
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $newPlan = SubscriptionPlan::findOrFail($request->input('new_plan_id'));
            $recalculateDays = $request->boolean('recalculate_days', false);

            return DB::transaction(function () use ($request, $subscription, $newPlan, $recalculateDays) {
                $currentPlan = $subscription->plan;
                $remainingDays = 0;
                $newEndDate = null;
                $priceDifference = 0;

                // Рассчитываем оставшиеся дни
                if ($subscription->end_date && $subscription->end_date->isFuture()) {
                    $remainingDays = now()->diffInDays($subscription->end_date, false);
                }

                if ($recalculateDays && $remainingDays > 0) {
                    // Рассчитываем пропорциональную стоимость
                    $currentDailyPrice = $currentPlan->price / max($currentPlan->duration_days, 1);
                    $newDailyPrice = $newPlan->price / max($newPlan->duration_days, 1);

                    $currentValue = $currentDailyPrice * $remainingDays;
                    $newValue = $newDailyPrice * $remainingDays;
                    $priceDifference = $newValue - $currentValue;

                    // Если разница отрицательная (новый план дешевле), устанавливаем минимум 1 день
                    if ($priceDifference < 0) {
                        $newEndDate = now()->addDay();
                        $priceDifference = $newDailyPrice; // Цена за 1 день нового плана
                    } else {
                        $newEndDate = $subscription->end_date; // Сохраняем текущую дату окончания
                    }
                } else {
                    // Без пересчета - просто меняем план, сохраняя дату окончания
                    $newEndDate = $subscription->end_date;
                    $priceDifference = $newPlan->price; // Полная стоимость нового плана
                }

                // Создаем заказ для смены тарифа
                $order = $this->orderService->createVpnSubscriptionOrder(
                    $subscription->user,
                    $newPlan,
                    'upgrade'
                );

                // Обновляем сумму заказа с учетом разницы в цене
                if ($priceDifference > 0) {
                    $order->update(['total_amount' => (int)($priceDifference * 100)]); // Конвертируем в копейки
                }

                // Создаем платеж
                $paymentResult = $this->paymentService->createPayment(
                    $order,
                    $request->input('payment_method')
                );

                if (!$paymentResult->isSuccess()) {
                    throw new \Exception($paymentResult->message ?? 'Ошибка создания платежа');
                }

                // Меняем тариф
                $subscription = $this->subscriptionService->manualUpgradeSubscription(
                    $subscription,
                    $newPlan,
                    $order,
                    $newEndDate,
                    $request->input('notes'),
                    $request->input('admin_notes')
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Тариф изменен',
                    'data' => [
                        'subscription' => $subscription,
                        'price_difference' => $priceDifference,
                        'remaining_days' => $remainingDays,
                        'recalculated' => $recalculateDays,
                    ],
                    'payment_url' => $paymentResult->paymentUrl,
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Change subscription end date to a specific date
     */
    public function changeEndDate(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'end_date' => 'required|date|after:now',
                'notes' => 'nullable|string|max:1000',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $newEndDate = Carbon::parse($request->input('end_date'));

            $subscription = $this->subscriptionService->manualChangeSubscriptionEndDate(
                $subscription,
                $newEndDate,
                $request->input('notes'),
                $request->input('admin_notes')
            );

            return response()->json([
                'success' => true,
                'message' => 'Дата окончания подписки изменена',
                'data' => [
                    'subscription' => $subscription->load(['user', 'plan', 'order']),
                    'new_end_date' => $newEndDate->toDateTimeString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete subscription (soft delete or force delete)
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        try {
            $subscription = Subscription::findOrFail($id);
            $forceDelete = $request->boolean('force_delete', false);

            if ($forceDelete) {
                // Проверяем, можно ли удалить подписку навсегда
                if ($subscription->status === 'active') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Нельзя окончательно удалить активную подписку',
                    ], 422);
                }

                // Жесткое удаление
                $subscription->forceDelete();
                $message = 'Подписка удалена окончательно';
            } else {
                // Мягкое удаление
                $subscription->delete();
                $message = 'Подписка удалена (можно восстановить)';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при удалении подписки',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(?int $bytes = null): string
    {
        if ($bytes || $bytes === 0) return '0 Б';

        $units = ['Б', 'КБ', 'МБ', 'ГБ', 'ТБ'];
        $size = $bytes;
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }
}
