<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Составные индексы для оптимизации частых запросов
            $table->index(['status', 'end_date', 'deleted_at'], 'subscriptions_status_end_date_deleted_at_index');
            $table->index(['status', 'deleted_at'], 'subscriptions_status_deleted_at_index');
            $table->index(['user_id', 'status', 'deleted_at'], 'subscriptions_user_status_deleted_at_index');
            $table->index(['deleted_at'], 'subscriptions_deleted_at_index');
            $table->index(['status', 'created_at'], 'subscriptions_status_created_at_index');
        });

        Schema::table('users', function (Blueprint $table) {
            // Индекс для deleted_at
            $table->index(['deleted_at'], 'users_deleted_at_index');
            $table->index(['is_active', 'deleted_at'], 'users_is_active_deleted_at_index');
        });

        Schema::table('orders', function (Blueprint $table) {
            // Индекс для deleted_at
            $table->index(['deleted_at'], 'orders_deleted_at_index');
        });

        Schema::table('subscription_plans', function (Blueprint $table) {
            // Составные индексы для планов
            $table->index(['is_demo', 'is_active', 'deleted_at'], 'subscription_plans_demo_active_deleted_at_index');
            $table->index(['deleted_at'], 'subscription_plans_deleted_at_index');
        });

        Schema::table('user_traffic_logs', function (Blueprint $table) {
            // Оптимизированный индекс для частых запросов по user_id + subscription_id с сортировкой
            $table->index(['user_id', 'subscription_id', 'data_as_of_timestamp'], 'idx_user_subscription_timestamp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex('subscriptions_status_end_date_deleted_at_index');
            $table->dropIndex('subscriptions_status_deleted_at_index');
            $table->dropIndex('subscriptions_user_status_deleted_at_index');
            $table->dropIndex('subscriptions_deleted_at_index');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_deleted_at_index');
            $table->dropIndex('users_is_active_deleted_at_index');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('orders_deleted_at_index');
        });

        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->dropIndex('subscription_plans_demo_active_deleted_at_index');
            $table->dropIndex('subscription_plans_deleted_at_index');
        });

        Schema::table('user_traffic_logs', function (Blueprint $table) {
            $table->dropIndex('idx_user_subscription_timestamp');
        });
    }
};
