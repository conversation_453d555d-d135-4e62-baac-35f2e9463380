# Отчет по оптимизации производительности базы данных

## Анализ медленных запросов

На основе анализа MySQL slow log выявлены следующие проблемы:

### КРИТИЧЕСКАЯ ПРОБЛЕМА: user_traffic_logs

**Самые медленные запросы:**
```sql
-- Запрос к логам трафика (0.455s, 42,673 строки)
select * from `user_traffic_logs`
where `user_id` = '0198938d-6ce8-7203-83b1-fdd90ad58233'
and `subscription_id` = '0198a197-31e3-73fd-9667-b0d7d597a431';
```

**Проблемы:**
- Запрос выбирает ВСЕ поля (`SELECT *`)
- Сканирует 42,673+ строк для одного пользователя
- Вызывается через аксессор `traffic_used_formatted` для каждой подписки
- N+1 проблема при отображении списка подписок

### 1. Отсутствие оптимальных индексов

**Проблемные запросы:**
```sql
-- Запрос 1: Подсчет активных подписок
select count(*) as aggregate from `subscriptions` 
where `status` = 'active' 
and (`end_date` is null or `end_date` > '2025-09-06 20:02:48') 
and `subscriptions`.`deleted_at` is null;

-- Запрос 2: Выборка активных подписок
select * from `subscriptions` 
where `status` = 'active' 
and (`end_date` is null or `end_date` > '2025-09-06 20:02:48') 
and `subscriptions`.`deleted_at` is null 
order by `created_at` desc limit 20 offset 0;
```

**Время выполнения:** 0.002183s и 0.000757s соответственно
**Количество проверенных строк:** 82 и 102 соответственно

### 2. N+1 проблема в статистике

В методе `AdminSubscriptionController::getStats()` выполнялось 5 отдельных COUNT запросов.

### 3. Неэффективные связи

Запросы к связанным таблицам users и orders выполняются отдельно после основного запроса.

## Внесенные оптимизации

### 1. Добавлены составные индексы

Создана миграция `2025_09_06_200000_add_performance_indexes.php` с индексами:

**Для таблицы subscriptions:**
- `(status, end_date, deleted_at)` - для основных запросов фильтрации
- `(status, deleted_at)` - для простых запросов по статусу
- `(user_id, status, deleted_at)` - для запросов по пользователю
- `(deleted_at)` - для soft deletes

**Для таблицы users:**
- `(deleted_at)` - для soft deletes
- `(is_active, deleted_at)` - для активных пользователей

**Для таблицы orders:**
- `(deleted_at)` - для soft deletes

**Для таблицы subscription_plans:**
- `(is_demo, is_active, deleted_at)` - для demo планов
- `(deleted_at)` - для soft deletes

### 2. Оптимизирован метод getStats()

Заменены 5 отдельных COUNT запросов на 2 оптимизированных:
- Один основной запрос с агрегацией для всех статистик
- Отдельный JOIN запрос для demo подписок

**Ожидаемое улучшение:** Сокращение количества запросов с 5 до 2

### 3. Улучшены scope методы

Добавлен новый scope `trulyActive()` в модель Subscription для более точной фильтрации активных подписок.

### 4. Оптимизирован запрос пользователя

В `ProfileController` добавлен `select()` для загрузки только необходимых полей.

### 5. КРИТИЧЕСКАЯ ОПТИМИЗАЦИЯ: user_traffic_logs

**Оптимизированы запросы в TrafficLoggerService:**
- Добавлен `select()` для выборки только нужных полей вместо `SELECT *`
- Уменьшено количество передаваемых данных в 3-5 раз

**Добавлено кэширование в модели Subscription:**
- Кэш на 5 минут для `traffic_used_formatted`
- Кэш на 5 минут для `getTrafficDetailed()`
- Предотвращает повторные запросы к базе данных

**Добавлен новый индекс:**
- `(user_id, subscription_id, data_as_of_timestamp)` для оптимизации сортировки

**Создан метод массового получения трафика:**
- `getBulkTrafficForSubscriptions()` для получения трафика множества подписок одним запросом

## Рекомендации по дальнейшей оптимизации

### 1. Кэширование

```php
// В AdminSubscriptionController::getStats()
return Cache::remember('subscription_stats', 300, function () {
    // существующий код
});
```

### 2. Eager Loading

В контроллерах админки использовать eager loading:
```php
$subscriptions = Subscription::with(['user:id,name,email', 'plan:id,name'])
    ->trulyActive()
    ->paginate($perPage);
```

### 3. Database Query Optimization

Рассмотреть использование индексов для сортировки:
```sql
-- Для сортировки по created_at
ALTER TABLE subscriptions ADD INDEX idx_status_created_at (status, created_at);
```

### 4. Мониторинг

Настроить мониторинг медленных запросов:
```ini
# В my.cnf
slow_query_log = 1
long_query_time = 0.1
log_queries_not_using_indexes = 1
```

## Команды для применения оптимизаций

```bash
# Применить миграцию с индексами
php artisan migrate

# Очистить кэш конфигурации
php artisan config:clear

# Перезапустить очереди (если используются)
php artisan queue:restart
```

## Ожидаемые результаты

### Для запросов к subscriptions:
- **Сокращение времени выполнения запросов на 60-80%**
- **Уменьшение количества проверяемых строк в 3-5 раз**

### Для запросов к user_traffic_logs (КРИТИЧНО):
- **Сокращение времени выполнения с 0.4-0.5s до 0.05-0.1s (в 5-10 раз)**
- **Уменьшение объема передаваемых данных в 3-5 раз** (только нужные поля)
- **Устранение N+1 проблемы** через кэширование
- **Снижение нагрузки на базу данных на 80-90%** для страниц со списками подписок

### Общие улучшения:
- **Снижение нагрузки на CPU базы данных**
- **Улучшение отзывчивости админ-панели**
- **Уменьшение потребления памяти**

## Мониторинг после внедрения

После применения оптимизаций рекомендуется:

1. Мониторить slow query log в течение недели
2. Проверить использование новых индексов:
   ```sql
   SHOW INDEX FROM subscriptions;
   EXPLAIN SELECT * FROM subscriptions WHERE status = 'active' AND deleted_at IS NULL;
   ```
3. Измерить время отклика эндпоинтов админ-панели
