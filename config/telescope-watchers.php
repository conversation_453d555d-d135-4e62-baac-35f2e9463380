<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Telescope Watchers Configuration
    |--------------------------------------------------------------------------
    |
    | This file allows you to easily enable/disable specific Telescope watchers
    | and configure their settings. You can override these settings using
    | environment variables for different environments.
    |
    */

    'profiles' => [
        /*
        |--------------------------------------------------------------------------
        | Development Profile
        |--------------------------------------------------------------------------
        | Full monitoring for development environment
        */
        'development' => [
            'batch' => true,
            'cache' => true,
            'client_request' => true,
            'command' => true,
            'dump' => true,
            'event' => true,
            'exception' => true,
            'gate' => true,
            'job' => true,
            'log' => true,
            'mail' => true,
            'model' => true,
            'notification' => true,
            'query' => true,
            'redis' => true,
            'request' => true,
            'schedule' => true,
            'view' => true,
        ],

        /*
        |--------------------------------------------------------------------------
        | Production Profile
        |--------------------------------------------------------------------------
        | Minimal monitoring for production environment
        */
        'production' => [
            'batch' => false,
            'cache' => false,
            'client_request' => false,
            'command' => false,
            'dump' => false,
            'event' => false,
            'exception' => true,  // Always monitor exceptions
            'gate' => false,
            'job' => true,        // Monitor jobs
            'log' => true,        // Monitor logs (errors only)
            'mail' => false,
            'model' => false,
            'notification' => false,
            'query' => false,     // Can be enabled for performance debugging
            'redis' => false,
            'request' => false,   // Can be enabled for specific debugging
            'schedule' => true,   // Monitor scheduled tasks
            'view' => false,
        ],

        /*
        |--------------------------------------------------------------------------
        | Debug Profile
        |--------------------------------------------------------------------------
        | For debugging specific issues
        */
        'debug' => [
            'batch' => false,
            'cache' => true,
            'client_request' => false,
            'command' => false,
            'dump' => true,
            'event' => true,
            'exception' => true,
            'gate' => false,
            'job' => true,
            'log' => true,
            'mail' => true,
            'model' => true,
            'notification' => true,
            'query' => true,      // Enable for database debugging
            'redis' => true,
            'request' => true,    // Enable for request debugging
            'schedule' => false,
            'view' => false,
        ],

        /*
        |--------------------------------------------------------------------------
        | Performance Profile
        |--------------------------------------------------------------------------
        | For performance monitoring
        */
        'performance' => [
            'batch' => true,
            'cache' => true,
            'client_request' => true,
            'command' => false,
            'dump' => false,
            'event' => false,
            'exception' => true,
            'gate' => false,
            'job' => true,
            'log' => false,
            'mail' => false,
            'model' => false,
            'notification' => false,
            'query' => true,      // Monitor slow queries
            'redis' => true,
            'request' => true,    // Monitor request performance
            'schedule' => true,
            'view' => false,
        ],

        /*
        |--------------------------------------------------------------------------
        | Minimal Profile
        |--------------------------------------------------------------------------
        | Only essential monitoring
        */
        'minimal' => [
            'batch' => false,
            'cache' => false,
            'client_request' => false,
            'command' => false,
            'dump' => false,
            'event' => false,
            'exception' => true,  // Only exceptions
            'gate' => false,
            'job' => false,
            'log' => true,        // Only error logs
            'mail' => false,
            'model' => false,
            'notification' => false,
            'query' => false,
            'redis' => false,
            'request' => false,
            'schedule' => false,
            'view' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Current Profile
    |--------------------------------------------------------------------------
    |
    | Set the current profile to use. Can be overridden with
    | TELESCOPE_PROFILE environment variable.
    |
    */
    'current_profile' => env('TELESCOPE_PROFILE', 'development'),

    /*
    |--------------------------------------------------------------------------
    | Custom Watcher Settings
    |--------------------------------------------------------------------------
    |
    | Override specific watcher settings here
    |
    */
    'settings' => [
        'query' => [
            'slow' => env('TELESCOPE_QUERY_SLOW_THRESHOLD', 100), // ms
        ],
        'log' => [
            'level' => env('TELESCOPE_LOG_LEVEL', 'error'),
        ],
        'request' => [
            'size_limit' => env('TELESCOPE_RESPONSE_SIZE_LIMIT', 64), // KB
        ],
    ],
];
